#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于RectPack的完全统一布局工作器
完全使用RectPack算法，不再进行A/B/C分类，实现最优画布利用率

特性：
1. 完全移除A/B/C分类逻辑
2. 使用RectPack算法统一处理所有图片
3. 自动优化图片旋转和排列
4. 实现最大化画布利用率
5. 简化的处理流程
"""

import os
import time
import logging
from typing import List, Dict, Any
from PyQt6.QtCore import QThread, pyqtSignal, QMutex, QMutexLocker

# 设置日志
log = logging.getLogger(__name__)

# 导入核心模块
from core.excel_processor import ExcelProcessor
from core.image_indexer_duckdb import ImageIndexerDuckDB
from core.unified_image_arranger import UnifiedImageArranger

from utils.photoshop_helper import PhotoshopHelper
from utils.memory_manager import MemoryManager
from utils.image_processor import get_image_processor

# 配置日志
from utils.log_config import get_logger
log = get_logger("RectPackLayoutWorker")

class RectPackLayoutWorker(QThread):
    """
    基于RectPack的完全统一布局工作器

    完全使用RectPack算法，移除所有A/B/C分类逻辑
    """

    # 信号定义
    progress_signal = pyqtSignal(int)  # 进度信号 (0-100)
    log_signal = pyqtSignal(str)       # 日志信号
    error_signal = pyqtSignal(str)     # 错误信号
    finished_signal = pyqtSignal()     # 完成信号
    stage_signal = pyqtSignal(str, int, str)  # 阶段信号 (阶段名, 进度, 状态)
    new_canvas_needed = pyqtSignal(str, list)  # 新画布需求信号，参数为材质名称和剩余图案

    def __init__(self, config_manager, image_indexer: ImageIndexerDuckDB,
                 excel_processor: ExcelProcessor, parent=None):
        """
        初始化RectPack布局工作器

        Args:
            config_manager: 配置管理器
            image_indexer: 图片索引器
            excel_processor: Excel处理器
            parent: 父对象
        """
        super().__init__(parent)

        self.config_manager = config_manager
        self.image_indexer = image_indexer
        self.excel_processor = excel_processor

        # 工作参数
        self.library_path = ""
        self.material_folder_path = ""
        self.canvas_width_m = 2.0
        self.max_height_cm = 5000
        self.ppi = 72
        self.image_spacing_cm = 0.1
        self.horizontal_expansion_cm = 0

        # 控制标志
        self._stop_requested = False
        self._mutex = QMutex()

        # 核心组件
        self.unified_arranger = None

        # 性能监控
        self.memory_manager = MemoryManager()

        # 状态属性
        self.success = False  # 添加success属性
        self.successful_arrangements = 0
        self.failed_arrangements = 0
        self.start_time = None

        # 多画布支持属性
        self.canvas_name = ""
        self.material_name = ""
        self.output_path = ""
        self.canvas_sequence = 1  # 画布序号
        self.remaining_patterns = []  # 未处理完的图案

        # 图案数据
        self.pattern_items = []  # 从主应用程序接收的图案数据
        self.use_external_patterns = False  # 是否使用外部提供的图案数据

        # 图片处理器
        self.image_processor = None

    def set_parameters(self, library_path: str, material_folder_path: str,
                      canvas_width_m: float, max_height_cm: float, ppi: float,
                      image_spacing_cm: float, horizontal_expansion_cm: float = 0):
        """
        设置工作参数

        Args:
            library_path: 图库路径
            material_folder_path: 材质文件夹路径
            canvas_width_m: 画布宽度（米）
            max_height_cm: 最大高度（厘米）
            ppi: 每英寸像素数
            image_spacing_cm: 图片间距（厘米）
            horizontal_expansion_cm: 水平扩展（厘米）
        """
        with QMutexLocker(self._mutex):
            self.library_path = library_path
            self.material_folder_path = material_folder_path
            self.canvas_width_m = canvas_width_m
            self.max_height_cm = max_height_cm
            self.ppi = ppi
            self.image_spacing_cm = image_spacing_cm
            self.horizontal_expansion_cm = horizontal_expansion_cm

    def set_canvas_info(self, canvas_name: str, material_name: str, output_path: str, canvas_sequence: int = 1):
        """
        设置画布信息（用于多画布支持）

        Args:
            canvas_name: 画布名称
            material_name: 材质名称
            output_path: 输出路径
            canvas_sequence: 画布序号
        """
        with QMutexLocker(self._mutex):
            self.canvas_name = canvas_name
            self.material_name = material_name
            self.output_path = output_path
            self.canvas_sequence = canvas_sequence

    def set_pattern_items(self, pattern_items: List[Dict[str, Any]]):
        """
        设置图案数据（从主应用程序接收）

        Args:
            pattern_items: 图案数据列表
        """
        with QMutexLocker(self._mutex):
            self.pattern_items = pattern_items
            self.use_external_patterns = True
            self.log_signal.emit(f"接收到 {len(pattern_items)} 个图案数据")

    def request_stop(self):
        """请求停止工作"""
        with QMutexLocker(self._mutex):
            self._stop_requested = True
        self.log_signal.emit("收到停止请求...")

    def stop(self):
        """停止工作（兼容性方法）"""
        self.request_stop()

    def is_stop_requested(self) -> bool:
        """检查是否请求停止"""
        with QMutexLocker(self._mutex):
            return self._stop_requested

    def emit_stage(self, stage_name: str, progress: int, status: str):
        """发送阶段信号"""
        self.stage_signal.emit(stage_name, progress, status)
        self.log_signal.emit(f"[{stage_name}] {status}")

    def run(self):
        """主工作流程"""
        try:
            self.start_time = time.time()
            self._stop_requested = False
            self.success = False  # 初始化为失败状态

            self.log_signal.emit("=" * 60)
            self.log_signal.emit("开始RectPack统一布局处理")
            self.log_signal.emit("=" * 60)

            # 阶段1: 准备工作
            self.emit_stage("准备", 0, "初始化组件...")
            if not self._initialize_components():
                self.error_signal.emit("组件初始化失败")
                return

            if self.is_stop_requested():
                self.log_signal.emit("用户取消了操作")
                return

            # 阶段2: 获取图案数据
            if self.use_external_patterns and self.pattern_items:
                # 使用从主应用程序接收的图案数据
                self.emit_stage("检索", 10, "使用已提供的图案数据...")
                pattern_items = self.pattern_items
                self.log_signal.emit(f"使用已提供的 {len(pattern_items)} 个图案数据")
            else:
                # 从 Excel 文件处理图案数据
                self.emit_stage("检索", 10, "处理材质表格...")
                pattern_items = self._process_excel_files()
                if not pattern_items:
                    self.error_signal.emit("没有找到有效的图片数据，请检查Excel文件")
                    return

            if self.is_stop_requested():
                self.log_signal.emit("用户取消了操作")
                return

            # 阶段3: 检索图片（测试模式下跳过）
            self.emit_stage("检索", 30, "检索图片文件...")

            # 检查是否为测试模式
            test_mode_settings = self.config_manager.get_test_mode_settings()
            is_test_mode = test_mode_settings.get('is_test_mode', False)

            if is_test_mode:
                self.log_signal.emit("测试模式: 跳过图片检索，直接使用色块表示图片")
                retrieved_patterns = pattern_items  # 在测试模式下直接使用原始数据

                # 为测试模式添加颜色信息
                colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']
                for i, pattern in enumerate(retrieved_patterns):
                    pattern['color'] = colors[i % len(colors)]
                    pattern['test_mode'] = True
                    # 在测试模式下，设置一个虚拟的图片路径
                    if not pattern.get('path') or pattern.get('path') == '未入库':
                        pattern['path'] = f"test_mode_color_block_{i}.png"

                self.log_signal.emit(f"测试模式: 将使用 {len(retrieved_patterns)} 个色块进行排列")
            else:
                # 非测试模式下正常检索图片
                retrieved_patterns = self._retrieve_images(pattern_items)
                if not retrieved_patterns:
                    self.error_signal.emit("没有找到匹配的图片文件，请检查图库索引和图案名称")
                    return

                # 检查是否有足够的图片进行排列
                if len(retrieved_patterns) < len(pattern_items) * 0.1:  # 如果找到的图片少于10%
                    self.log_signal.emit(f"警告: 只找到 {len(retrieved_patterns)}/{len(pattern_items)} 个图片，成功率较低")
                    self.log_signal.emit("建议检查图案名称与图库文件名的匹配规则")

            if self.is_stop_requested():
                self.log_signal.emit("用户取消了操作")
                return

            # 阶段4: RectPack统一排列（跳过分类步骤）
            self.emit_stage("排列", 50, "使用RectPack算法统一排列所有图片...")
            arranged_images = self._arrange_images_unified(retrieved_patterns)
            if not arranged_images:
                self.error_signal.emit("图片排列失败，可能是图片尺寸过大或算法错误")
                return

            if self.is_stop_requested():
                self.log_signal.emit("用户取消了操作")
                return

            # 阶段5: 输出到Photoshop
            self.emit_stage("输出", 80, "创建Photoshop画布...")
            success = self._create_photoshop_canvas(arranged_images)
            if not success:
                self.error_signal.emit("Photoshop画布创建失败，请检查Photoshop是否正常运行")
                return

            # 检查是否有剩余图片需要新画布
            if self.remaining_patterns and len(self.remaining_patterns) > 0:
                self.log_signal.emit(f"有 {len(self.remaining_patterns)} 个图片未能放置，需要创建新画布")

                # 发送新画布需求信号
                if self.material_name:
                    self.new_canvas_needed.emit(self.material_name, self.remaining_patterns)
                else:
                    self.log_signal.emit("警告: 未设置材质名称，无法创建新画布")
            else:
                self.log_signal.emit("所有图片已成功放置")

            # 完成
            self.emit_stage("输出", 100, "处理完成")
            self._log_final_statistics()
            self.success = True  # 设置成功标志
            self.log_signal.emit("✅ RectPack算法处理成功完成！")

        except Exception as e:
            error_msg = f"RectPack布局处理发生严重错误: {str(e)}"
            self.error_signal.emit(error_msg)
            log.error(error_msg, exc_info=True)
            self.success = False  # 设置失败标志

            # 输出详细的错误信息
            self.log_signal.emit("❌ 发生严重错误，处理中断")
            self.log_signal.emit(f"错误类型: {type(e).__name__}")
            self.log_signal.emit(f"错误详情: {str(e)}")

        finally:
            # 确保始终发送完成信号
            self.finished_signal.emit()

    def _initialize_components(self) -> bool:
        """初始化组件"""
        try:
            # 计算画布参数
            canvas_width_cm = self.canvas_width_m * 100 + self.horizontal_expansion_cm
            canvas_width_px = int(canvas_width_cm * 0.393701 * self.ppi)

            # 限制最大高度，避免过大的画布导致性能问题
            max_reasonable_height_cm = 5000  # 50米最大高度
            actual_max_height_cm = min(self.max_height_cm, max_reasonable_height_cm)
            max_height_px = int(actual_max_height_cm * 0.393701 * self.ppi)

            image_spacing_px = int(self.image_spacing_cm * 0.393701 * self.ppi)

            # 记录限制信息
            if self.max_height_cm > max_reasonable_height_cm:
                self.log_signal.emit(f"警告: 设置的最大高度 {self.max_height_cm}cm 超过合理范围，已限制为 {actual_max_height_cm}cm")

            # 初始化统一排列器
            self.unified_arranger = UnifiedImageArranger(log_signal=self.log_signal)
            self.unified_arranger.initialize(
                canvas_width_px=canvas_width_px,
                max_height_px=max_height_px,
                image_spacing_px=image_spacing_px,
                ppi=self.ppi
            )

            # 初始化图片处理器，与俄罗斯方块算法保持一致
            is_test_mode = False
            test_mode_config = {
                'miniature_ratio': 0.02,
                'is_test_all_data': False
            }

            try:
                if hasattr(self, 'config_manager') and self.config_manager:
                    test_mode_settings = self.config_manager.get_test_mode_settings()
                    is_test_mode = test_mode_settings.get('is_test_mode', False)
                    test_mode_config = {
                        'miniature_ratio': test_mode_settings.get('miniature_ratio', 0.02),
                        'is_test_all_data': test_mode_settings.get('is_test_all_data', False)
                    }
                    self.log_signal.emit(f"测试模式设置: 启用={is_test_mode}, 缩小比率={test_mode_config['miniature_ratio']}, 测试全部数据={test_mode_config['is_test_all_data']}")
                else:
                    self.log_signal.emit("未设置配置管理器，使用默认测试模式设置")
            except Exception as e:
                self.log_signal.emit(f"获取测试模式设置失败: {str(e)}，使用默认设置")

            self.image_processor = get_image_processor(is_test_mode, test_mode_config)

            self.log_signal.emit(f"RectPack组件初始化完成")
            self.log_signal.emit(f"画布参数: 宽度={canvas_width_cm:.1f}cm ({canvas_width_px}px), 最大高度={actual_max_height_cm}cm ({max_height_px}px)")
            self.log_signal.emit(f"图片间距: {self.image_spacing_cm}cm ({image_spacing_px}px), PPI={self.ppi}")

            return True

        except Exception as e:
            self.error_signal.emit(f"组件初始化失败: {str(e)}")
            return False

    def _process_excel_files(self) -> List[Dict[str, Any]]:
        """处理Excel文件 - 使用与俄罗斯方块算法相同的数据提取逻辑"""
        try:
            if not os.path.exists(self.material_folder_path):
                self.error_signal.emit(f"材质文件夹不存在: {self.material_folder_path}")
                return []

            # 查找已检索的Excel文件（优先使用）
            indexed_files = [f for f in os.listdir(self.material_folder_path)
                           if f.endswith('_已检索.xlsx')]

            # 如果没有已检索的文件，查找原始Excel文件
            if not indexed_files:
                excel_files = [f for f in os.listdir(self.material_folder_path)
                              if f.lower().endswith(('.xlsx', '.xls')) and not f.endswith('_已检索.xlsx')]
                if not excel_files:
                    self.error_signal.emit("材质文件夹中没有找到Excel文件")
                    return []
                self.log_signal.emit(f"找到 {len(excel_files)} 个原始Excel文件，将直接处理")
                files_to_process = excel_files
            else:
                self.log_signal.emit(f"找到 {len(indexed_files)} 个已检索的Excel文件，优先使用")
                files_to_process = indexed_files

            all_patterns = []

            # 获取配置参数
            exact_pattern_search = False
            is_standard_mode = True
            is_fuzzy_query = False

            try:
                if hasattr(self, 'config_manager') and self.config_manager:
                    exact_pattern_search = self.config_manager.get('exact_pattern_search', False)
                    is_standard_mode = self.config_manager.get('is_standard_mode', True)
                    is_fuzzy_query = self.config_manager.get('is_fuzzy_query', False)
                    self.log_signal.emit(f"Excel处理参数: 精确查询={exact_pattern_search}, 标准模式={is_standard_mode}, 模糊查询={is_fuzzy_query}")
            except Exception as e:
                self.log_signal.emit(f"获取Excel处理参数失败: {str(e)}，使用默认参数")

            for excel_file in files_to_process:
                if self.is_stop_requested():
                    break

                excel_path = os.path.join(self.material_folder_path, excel_file)
                self.log_signal.emit(f"处理Excel文件: {excel_file}")

                try:
                    # 使用与俄罗斯方块算法完全相同的Excel处理器
                    # 无论是已检索文件还是原始文件，都使用统一的处理逻辑
                    self.log_signal.emit(f"开始处理Excel文件: {excel_file}")

                    # 先检查Excel文件的基本信息
                    try:
                        import pandas as pd
                        xls = pd.ExcelFile(excel_path)
                        self.log_signal.emit(f"Excel文件包含 {len(xls.sheet_names)} 个工作表: {', '.join(xls.sheet_names)}")

                        # 检查第一个工作表的列名
                        if xls.sheet_names:
                            first_sheet = xls.sheet_names[0]
                            df_sample = pd.read_excel(excel_path, sheet_name=first_sheet, nrows=0)  # 只读取表头
                            self.log_signal.emit(f"工作表 '{first_sheet}' 的原始列名: {list(df_sample.columns)}")
                    except Exception as e:
                        self.log_signal.emit(f"检查Excel文件信息时发生错误: {str(e)}")

                    patterns = self.excel_processor.process_excel_file(
                        excel_path,
                        image_indexer=self.image_indexer,
                        exact_pattern_search=exact_pattern_search,
                        is_standard_mode=is_standard_mode,
                        is_fuzzy_query=is_fuzzy_query
                    )

                    if patterns:
                        all_patterns.extend(patterns)
                        self.log_signal.emit(f"✅ 从 {excel_file} 提取了 {len(patterns)} 个图案")

                        # 显示前几个图案的信息作为调试
                        for i, pattern in enumerate(patterns[:3]):
                            self.log_signal.emit(f"  图案 {i+1}: {pattern.get('pattern_name', '未知')} - "
                                               f"{pattern.get('width_cm', 0)}x{pattern.get('height_cm', 0)}cm")
                    else:
                        self.log_signal.emit(f"❌ 警告: 从 {excel_file} 未提取到任何图案")

                        # 尝试直接读取Excel文件来诊断问题
                        try:
                            import pandas as pd
                            xls = pd.ExcelFile(excel_path)
                            self.log_signal.emit(f"诊断信息: Excel文件包含 {len(xls.sheet_names)} 个工作表")

                            for sheet_name in xls.sheet_names:
                                df = pd.read_excel(excel_path, sheet_name=sheet_name, nrows=1)
                                self.log_signal.emit(f"  工作表 '{sheet_name}': {len(df.columns)} 列 - {list(df.columns)}")

                                # 检查是否有数据
                                total_rows = len(pd.read_excel(excel_path, sheet_name=sheet_name))
                                self.log_signal.emit(f"    数据行数: {total_rows}")

                        except Exception as diag_e:
                            self.log_signal.emit(f"诊断 Excel文件时发生错误: {str(diag_e)}")

                except Exception as e:
                    self.log_signal.emit(f"处理Excel文件 {excel_file} 时发生错误: {str(e)}")
                    log.error(f"处理Excel文件 {excel_file} 时发生错误: {str(e)}", exc_info=True)
                    continue

            self.log_signal.emit(f"总共提取了 {len(all_patterns)} 个图案")

            # 添加详细的调试信息
            if len(all_patterns) == 0:
                self.log_signal.emit("❌ 警告: 未提取到任何图案数据")
                self.log_signal.emit("")
                self.log_signal.emit("🔍 请检查以下问题:")
                self.log_signal.emit("1. Excel文件是否包含有效数据")
                self.log_signal.emit("2. 表格列名是否正确（需要: 图案/图案全称、宽度、高度、数量）")
                self.log_signal.emit("3. 数据行是否包含有效的尺寸和图案信息")
                self.log_signal.emit("4. 如果使用已检索文件，请检查文件是否完整")

                # 列出Excel文件信息
                self.log_signal.emit("")
                self.log_signal.emit("📄 Excel文件信息:")
                for excel_file in files_to_process:
                    excel_path = os.path.join(self.material_folder_path, excel_file)
                    try:
                        import pandas as pd
                        xls = pd.ExcelFile(excel_path)
                        self.log_signal.emit(f"  {excel_file}: {len(xls.sheet_names)} 个工作表 ({', '.join(xls.sheet_names[:3])}{'...' if len(xls.sheet_names) > 3 else ''})")
                    except Exception as e:
                        self.log_signal.emit(f"  {excel_file}: 读取失败 - {str(e)}")

            return all_patterns

        except Exception as e:
            error_msg = f"处理Excel文件时发生严重错误: {str(e)}"
            self.error_signal.emit(error_msg)
            log.error(error_msg, exc_info=True)
            return []



    def _retrieve_images(self, pattern_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """检索图片文件 - 使用与俄罗斯方块算法相同的检索逻辑"""
        try:
            # 检查图片索引器是否可用
            if not self.image_indexer:
                self.error_signal.emit("图片索引器未初始化")
                return []

            # 检查图库是否已索引
            if not self.image_indexer.is_indexed(self.library_path):
                self.error_signal.emit("图库尚未索引，请先索引图库")
                return []

            retrieved_patterns = []
            total_patterns = len(pattern_items)
            found_count = 0
            not_found_count = 0
            error_count = 0
            skipped_count = 0

            self.log_signal.emit(f"开始检索 {total_patterns} 个图案的图片文件...")
            self.log_signal.emit(f"图库路径: {self.library_path}")

            # 获取测试模式设置
            test_mode_settings = self.config_manager.get_test_mode_settings()
            is_test_mode = test_mode_settings.get('is_test_mode', False)
            is_test_all_data = test_mode_settings.get('is_test_all_data', False)

            # 减少批量大小以避免过多日志输出
            batch_size = 50
            for batch_start in range(0, total_patterns, batch_size):
                if self.is_stop_requested():
                    break

                batch_end = min(batch_start + batch_size, total_patterns)
                batch_patterns = pattern_items[batch_start:batch_end]

                # 只在处理大量数据时显示批次信息
                if total_patterns > 100:
                    self.log_signal.emit(f"处理第 {batch_start//batch_size + 1} 批次（{batch_start+1}-{batch_end}/{total_patterns}）")

                for i, pattern in enumerate(batch_patterns):
                    actual_index = batch_start + i

                    if self.is_stop_requested():
                        break

                    pattern_name = pattern.get('pattern_name', '')
                    existing_path = pattern.get('path', '')

                    # 跳过无效的图案名称
                    if not pattern_name or pattern_name == '未命名图案':
                        skipped_count += 1
                        continue

                    try:
                        # 检查是否已经有有效的图片路径
                        if existing_path and existing_path != '未入库':
                            if os.path.exists(existing_path):
                                retrieved_patterns.append(pattern)
                                found_count += 1
                                continue
                            else:
                                self.log_signal.emit(f"图片路径无效，重新查找: {pattern_name} -> {existing_path}")

                        # 在测试模式下，如果开启了测试全部数据，则跳过"未入库"的图片
                        if is_test_mode and not is_test_all_data and existing_path == '未入库':
                            skipped_count += 1
                            continue

                        # 使用图片索引器查找图片
                        image_path = None
                        try:
                            # 首先尝试精确匹配
                            image_path = self.image_indexer.find_image(pattern_name, exact_match=True)

                            # 如果精确匹配失败，尝试模糊匹配
                            if not image_path:
                                image_path = self.image_indexer.find_image(pattern_name, exact_match=False)

                        except Exception as e:
                            # 捕获数据库连接错误等问题
                            if "database" in str(e).lower() or "connection" in str(e).lower():
                                self.log_signal.emit(f"数据库连接问题，尝试重新连接: {str(e)}")
                                try:
                                    # 尝试重新连接数据库
                                    if hasattr(self.image_indexer, '_reconnect_database'):
                                        self.image_indexer._reconnect_database()
                                        # 重试查找
                                        image_path = self.image_indexer.find_image(pattern_name, exact_match=True)
                                except Exception as retry_e:
                                    self.log_signal.emit(f"重新连接失败: {str(retry_e)}")
                                    error_count += 1
                                    continue
                            else:
                                self.log_signal.emit(f"查找图片时发生错误 {pattern_name}: {str(e)}")
                                log.error(f"查找图片时发生错误 {pattern_name}: {str(e)}", exc_info=True)
                                error_count += 1
                                continue

                        if image_path and os.path.exists(image_path):
                            pattern['path'] = image_path
                            retrieved_patterns.append(pattern)
                            found_count += 1
                            # 只显示前10个找到的图片，避免日志过多
                            if found_count <= 10:
                                self.log_signal.emit(f"✓ 找到图片: {pattern_name} -> {os.path.basename(image_path)}")
                        else:
                            # 只记录前20个未找到的图片，避免日志过多
                            if not_found_count < 20:
                                self.log_signal.emit(f"未找到图片: {pattern_name}")
                            not_found_count += 1

                    except Exception as e:
                        self.log_signal.emit(f"处理图案时发生错误 {pattern_name}: {str(e)}")
                        log.error(f"处理图案时发生错误 {pattern_name}: {str(e)}", exc_info=True)
                        error_count += 1

                    # 更新进度
                    progress = int((actual_index + 1) / total_patterns * 20) + 30  # 30-50%
                    self.progress_signal.emit(progress)

                # 每批次处理后输出进度（仅在处理大量数据时）
                if total_patterns > 100:
                    self.log_signal.emit(f"第 {batch_start//batch_size + 1} 批次完成，已找到 {found_count} 个图片")

            # 输出详细统计信息
            self.log_signal.emit("=" * 50)
            self.log_signal.emit(f"RectPack图片检索统计:")
            self.log_signal.emit(f"  总图案数: {total_patterns}")
            self.log_signal.emit(f"  找到图片: {found_count} 个")
            self.log_signal.emit(f"  未找到: {not_found_count} 个")
            self.log_signal.emit(f"  跳过: {skipped_count} 个")
            self.log_signal.emit(f"  错误: {error_count} 个")
            self.log_signal.emit(f"  可用于排列: {len(retrieved_patterns)} 个")
            self.log_signal.emit("=" * 50)

            # 如果没有找到任何图片，提供详细的诊断信息
            if len(retrieved_patterns) == 0:
                self.log_signal.emit("❌ 警告: 没有找到任何可用的图片文件")
                self.log_signal.emit("")
                self.log_signal.emit("🔍 请检查以下问题:")
                self.log_signal.emit("1. 图库是否已正确索引")
                self.log_signal.emit("2. Excel文件中的图案名称是否与图库中的文件名匹配")
                self.log_signal.emit("3. 图库路径是否正确")
                self.log_signal.emit("4. 图片文件是否存在于图库目录中")

                # 显示一些示例图案名称
                if pattern_items:
                    self.log_signal.emit("")
                    self.log_signal.emit("📋 Excel中的图案名称示例:")
                    for i, pattern in enumerate(pattern_items[:5]):
                        pattern_name = pattern.get('pattern_name', '未知')
                        self.log_signal.emit(f"  {i+1}. {pattern_name}")
                    if len(pattern_items) > 5:
                        self.log_signal.emit(f"  ... 还有 {len(pattern_items) - 5} 个图案")

                # 尝试显示图库中的一些文件名
                try:
                    if self.image_indexer and self.image_indexer.db:
                        sample_files = self.image_indexer.db.execute(
                            "SELECT name_without_ext FROM image_files LIMIT 5"
                        ).fetchall()
                        if sample_files:
                            self.log_signal.emit("")
                            self.log_signal.emit("📁 图库中的文件名示例:")
                            for i, (filename,) in enumerate(sample_files):
                                self.log_signal.emit(f"  {i+1}. {filename}")
                        else:
                            self.log_signal.emit("")
                            self.log_signal.emit("⚠️ 图库索引为空，请重新索引图库")
                except Exception as e:
                    self.log_signal.emit(f"无法获取图库文件示例: {str(e)}")

            # 如果未找到的图片过多，给出额外提示
            if not_found_count > 20:
                self.log_signal.emit(f"")
                self.log_signal.emit(f"⚠️ 注意: 还有 {not_found_count - 20} 个图片未找到（未全部显示）")
                self.log_signal.emit(f"建议检查图案名称与图库文件名的匹配规则")

            return retrieved_patterns

        except Exception as e:
            error_msg = f"检索图片时发生严重错误: {str(e)}"
            self.error_signal.emit(error_msg)
            log.error(error_msg, exc_info=True)
            return []

    def _arrange_images_unified(self, pattern_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """使用RectPack算法统一排列所有图片（不进行分类）"""
        try:
            self.log_signal.emit("=" * 50)
            self.log_signal.emit("开始RectPack统一排列算法")
            self.log_signal.emit("=" * 50)
            self.log_signal.emit(f"待排列图片总数: {len(pattern_items)}")
            self.log_signal.emit("注意: 使用RectPack算法，不再进行A/B/C分类")

            # 为所有图片添加唯一标识符
            for i, pattern in enumerate(pattern_items):
                if 'unique_id' not in pattern:
                    width_cm = pattern.get('width_cm', 0)
                    height_cm = pattern.get('height_cm', 0)
                    pattern_name = pattern.get('pattern_name', f'图片{i+1}')
                    pattern['unique_id'] = f"{pattern_name}_{width_cm:.1f}_{height_cm:.1f}_{i}"
                    pattern['index'] = i
                    pattern['row_number'] = i + 1

            # 检查统一排列器是否已初始化
            if not hasattr(self, 'unified_arranger') or self.unified_arranger is None:
                self.error_signal.emit("统一排列器未初始化")
                return []

            # 使用统一排列器排列所有图片
            arranged_images = self.unified_arranger.arrange_images(pattern_items)

            # 尝试优化布局
            if arranged_images:
                self.log_signal.emit("尝试优化布局以提高画布利用率...")
                self.unified_arranger.optimize_layout()

            # 收集未排列的图片（用于多画布支持）
            remaining_patterns = self._collect_remaining_patterns(pattern_items, arranged_images)

            # 获取布局统计信息
            stats = self.unified_arranger.get_layout_statistics()
            if stats:
                self.log_signal.emit("=" * 50)
                self.log_signal.emit("RectPack排列统计")
                self.log_signal.emit("=" * 50)
                self.log_signal.emit(f"成功排列: {len(arranged_images)}/{len(pattern_items)} 个图片")
                self.log_signal.emit(f"画布利用率: {stats.get('utilization_percent', 0):.2f}%")
                self.log_signal.emit(f"画布尺寸: {stats.get('container_width', 0)}x{stats.get('container_height', 0)}px")
                self.log_signal.emit("=" * 50)

            self.successful_arrangements = len(arranged_images)
            self.failed_arrangements = len(pattern_items) - len(arranged_images)

            # 存储剩余图案信息，用于后续处理
            self.remaining_patterns = remaining_patterns

            return arranged_images

        except Exception as e:
            self.error_signal.emit(f"RectPack统一排列时发生错误: {str(e)}")
            return []

    def _collect_remaining_patterns(self, original_patterns: List[Dict[str, Any]],
                                  arranged_images: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        收集未排列的图案（用于多画布支持）

        Args:
            original_patterns: 原始图案列表
            arranged_images: 已排列的图片列表

        Returns:
            List[Dict[str, Any]]: 未排列的图案列表
        """
        try:
            # 获取已排列图片的unique_id集合
            arranged_ids = set()
            for img in arranged_images:
                unique_id = img.get('unique_id')
                if unique_id:
                    arranged_ids.add(unique_id)

            # 找出未排列的图案
            remaining_patterns = []
            for pattern in original_patterns:
                unique_id = pattern.get('unique_id')
                if unique_id and unique_id not in arranged_ids:
                    remaining_patterns.append(pattern)

            if remaining_patterns:
                self.log_signal.emit(f"收集到 {len(remaining_patterns)} 个未排列的图案，将在新画布继续排列")

                # 显示前几个未排列的图案
                for i, pattern in enumerate(remaining_patterns[:5]):
                    pattern_name = pattern.get('pattern_name', '未知')
                    width_cm = pattern.get('width_cm', 0)
                    height_cm = pattern.get('height_cm', 0)
                    self.log_signal.emit(f"  未排列 {i+1}: {pattern_name} - {width_cm}x{height_cm}cm")

                if len(remaining_patterns) > 5:
                    self.log_signal.emit(f"  ... 还有 {len(remaining_patterns) - 5} 个图案未显示")

            return remaining_patterns

        except Exception as e:
            self.log_signal.emit(f"收集剩余图案时发生错误: {str(e)}")
            return []

    def _create_photoshop_canvas(self, arranged_images: List[Dict[str, Any]]) -> bool:
        """创建Photoshop画布"""
        try:
            # 检查是否处于测试模式，与俄罗斯方块算法保持一致
            is_test_mode = False
            try:
                if hasattr(self, 'config_manager') and self.config_manager:
                    test_mode_settings = self.config_manager.get_test_mode_settings()
                    is_test_mode = test_mode_settings.get('is_test_mode', False)
                else:
                    self.log_signal.emit("未设置配置管理器，默认为非测试模式")
            except Exception as e:
                self.log_signal.emit(f"获取测试模式设置失败: {str(e)}，默认为非测试模式")

            if is_test_mode:
                self.log_signal.emit("测试模式: 跳过Photoshop画布创建，生成JPG图片和说明文档")

                # 参照Tetris算法，只使用RectPack算法的测试模式支持生成JPG和说明文档
                success = self._create_test_mode_canvas_with_rectpack(arranged_images)

                if success:
                    self.log_signal.emit("✅ RectPack测试模式处理完成")
                else:
                    self.log_signal.emit("❌ RectPack测试模式处理失败")

                return success

            # 检查Photoshop连接
            success, message = PhotoshopHelper.check_photoshop()
            if not success:
                self.error_signal.emit(f"Photoshop未运行: {message}")
                return False

            # 获取画布尺寸
            stats = self.unified_arranger.get_layout_statistics()
            canvas_width_px = stats.get('container_width', 0)
            canvas_height_px = stats.get('container_height', 0)

            if canvas_width_px <= 0 or canvas_height_px <= 0:
                self.error_signal.emit("无效的画布尺寸")
                return False

            # 创建Photoshop文档
            self.log_signal.emit(f"创建Photoshop画布: {canvas_width_px}x{canvas_height_px}px")

            # 使用PhotoshopHelper创建画布和放置图片
            success = PhotoshopHelper.create_canvas_and_place_images(
                arranged_images,
                canvas_width_px,
                canvas_height_px,
                self.ppi,
                log_signal=self.log_signal,
                progress_signal=self.progress_signal
            )

            if success:
                self.log_signal.emit("Photoshop画布创建成功")

                # 同时使用RectPack算法的正式环境支持生成TIFF和说明文档
                self._create_production_canvas_with_rectpack(arranged_images)
            else:
                self.error_signal.emit("Photoshop画布创建失败")

            return success

        except Exception as e:
            self.error_signal.emit(f"创建Photoshop画布时发生错误: {str(e)}")
            return False

    def _log_final_statistics(self):
        """记录最终统计信息"""
        try:
            elapsed_time = time.time() - self.start_time if self.start_time else 0

            self.log_signal.emit("=" * 60)
            self.log_signal.emit("RectPack处理完成统计")
            self.log_signal.emit("=" * 60)
            self.log_signal.emit(f"总处理时间: {elapsed_time:.2f}秒")
            self.log_signal.emit(f"成功排列: {self.successful_arrangements} 个图片")
            self.log_signal.emit(f"排列失败: {self.failed_arrangements} 个图片")

            if elapsed_time > 0:
                speed = self.successful_arrangements / elapsed_time
                self.log_signal.emit(f"平均速度: {speed:.2f} 图片/秒")

            # 获取最终布局统计
            stats = self.unified_arranger.get_layout_statistics()
            if stats:
                self.log_signal.emit(f"最终画布利用率: {stats.get('utilization_percent', 0):.2f}%")
                self.log_signal.emit(f"最终画布尺寸: {stats.get('container_width', 0)}x{stats.get('container_height', 0)}px")

            self.log_signal.emit("🎉 RectPack算法处理完成！")
            self.log_signal.emit("✓ 实现了最优的画布空间利用率")
            self.log_signal.emit("✓ 简化了图片排列流程")
            self.log_signal.emit("✓ 移除了复杂的A/B/C分类逻辑")
            self.log_signal.emit("=" * 60)

        except Exception as e:
            log.error(f"记录统计信息时发生错误: {str(e)}")

    def _create_test_mode_canvas_with_rectpack(self, arranged_images: List[Dict[str, Any]]) -> bool:
        """
        使用新的RectPack测试模式创建画布和生成文档，完全按照test_rectpack_real_data.py标准

        Args:
            arranged_images: 排列好的图片列表

        Returns:
            bool: 是否成功
        """
        try:
            # 获取测试模式设置
            test_mode_settings = self.config_manager.get_test_mode_settings()
            miniature_ratio = test_mode_settings.get('miniature_ratio', 1.0)  # 新测试模式默认1.0

            # 获取画布配置
            stats = self.unified_arranger.get_layout_statistics()
            canvas_width_px = stats.get('container_width', 0)
            canvas_height_px = stats.get('container_height', 0)

            if canvas_width_px <= 0 or canvas_height_px <= 0:
                self.log_signal.emit("无效的画布尺寸，无法创建 RectPack 测试模式画布")
                return False

            # 转换为cm单位（用于新测试模式的配置）
            canvas_width_cm = canvas_width_px / 10  # 假设10px = 1cm
            canvas_height_cm = canvas_height_px / 10

            # 使用新的RectPack测试模式
            from core.rectpack_test_mode import run_rectpack_test_mode

            # 将arranged_images转换为pattern_items格式
            pattern_items = []
            for i, img in enumerate(arranged_images):
                # 从图片信息中提取尺寸
                width_cm = img.get('width_cm', img.get('width', 100) / 10)
                height_cm = img.get('height_cm', img.get('height', 100) / 10)
                pattern_name = img.get('name', f'Image_{i+1}')

                pattern_items.append({
                    'width_cm': width_cm,
                    'height_cm': height_cm,
                    'pattern_name': pattern_name,
                    'quantity': 1
                })

            self.log_signal.emit(f"开始RectPack新测试模式处理: {len(pattern_items)} 张图片")

            # 设置输出目录
            output_dir = self.output_path or "output"
            canvas_name = self.canvas_name or f"rectpack_canvas_{self.canvas_sequence}"
            test_output_dir = os.path.join(output_dir, f"{canvas_name}_rectpack_test")

            # 运行新的RectPack测试模式
            result = run_rectpack_test_mode(
                pattern_items=pattern_items,
                canvas_width_cm=canvas_width_cm,
                horizontal_expansion_cm=2,  # 默认2cm水平拓展
                max_height_cm=canvas_height_cm,
                image_spacing_cm=0.1,
                miniature_ratio=miniature_ratio,
                output_dir=test_output_dir,
                material_name=canvas_name
            )

            if result['success']:
                self.log_signal.emit("✅ RectPack 新测试模式处理完成！")
                self.log_signal.emit(f"📊 容器数量: {result['total_containers']}")
                self.log_signal.emit(f"🎨 图片总数: {result['total_images']}")
                self.log_signal.emit(f"📈 平均利用率: {result['avg_utilization_rate']:.2f}%")
                self.log_signal.emit(f"⚡ 处理速度: {result['processing_speed']:.1f} 图片/秒")
                self.log_signal.emit(f"🖼️ 可视化文件: {len(result['visualization_files'])}个")
                self.log_signal.emit(f"📄 文档文件: {len(result['documentation_files'])}个")
                self.log_signal.emit(f"📁 输出目录: {result['output_dir']}")

                # 更新进度到100%
                self.progress_signal.emit(100)

                return True
            else:
                self.log_signal.emit(f"✗ RectPack 新测试模式失败: {result.get('error', '未知错误')}")
                return False

        except Exception as e:
            self.log_signal.emit(f"RectPack 新测试模式创建画布时发生错误: {str(e)}")
            return False

    def _create_production_canvas_with_rectpack(self, arranged_images: List[Dict[str, Any]]) -> bool:
        """
        使用RectPack算法的正式环境支持创建画布和生成文档

        Args:
            arranged_images: 排列好的图片列表

        Returns:
            bool: 是否成功
        """
        try:
            # 获取画布尺寸
            stats = self.unified_arranger.get_layout_statistics()
            canvas_width_px = stats.get('container_width', 0)
            canvas_height_px = stats.get('container_height', 0)

            if canvas_width_px <= 0 or canvas_height_px <= 0:
                self.log_signal.emit("无效的画布尺寸，无法创建 RectPack 正式环境画布")
                return False

            # 删除未使用的厘米转换，新方法会自动处理单位转换

            # 使用RectPack算法的正式环境支持
            from core.rectpack_arranger import RectPackArranger

            # 创建临时的RectPack排列器用于正式环境输出
            production_arranger = RectPackArranger(
                container_width=canvas_width_px,
                image_spacing=0,  # 正式环境下不需要间距
                max_height=canvas_height_px,
                log_signal=self.log_signal
            )

            # 使用新的完整正式环境方法
            canvas_name = self.canvas_name or "rectpack_canvas"

            # 获取输出目录（与表格同目录）
            output_dir = os.path.dirname(self.output_path) if hasattr(self, 'output_path') and self.output_path else os.getcwd()

            success = production_arranger.create_complete_production_environment(
                arranged_images=arranged_images,
                canvas_name=canvas_name,
                material_name=self.material_name,
                canvas_sequence=self.canvas_sequence,
                output_dir=output_dir,
                ppi=self.ppi
            )

            if not success:
                self.log_signal.emit("创建 RectPack 正式环境失败")
                return False

            # 新的完整方法已经包含了TIFF保存和文档生成
            self.log_signal.emit(f"📈 画布利用率: {stats.get('utilization_percent', 0):.2f}%")
            self.log_signal.emit(f"🎨 排列图片: {len(arranged_images)}个")

            return True

        except Exception as e:
            self.log_signal.emit(f"RectPack 正式环境创建画布时发生错误: {str(e)}")
            return False

    # 旧的文档生成方法已移除，现在使用RectPack排列器的新文档生成方法

    def _setup_matplotlib_chinese_font(self):
        """
        设置 matplotlib 中文字体，解决中文乱码问题 - 增强版
        """
        try:
            import matplotlib.pyplot as plt
            import matplotlib.font_manager as fm
            import platform
            import os

            # 根据操作系统选择合适的中文字体
            system = platform.system()

            if system == 'Windows':
                # Windows系统中文字体路径和名称
                font_configs = [
                    ('SimHei', ['C:/Windows/Fonts/simhei.ttf']),
                    ('Microsoft YaHei', ['C:/Windows/Fonts/msyh.ttc', 'C:/Windows/Fonts/msyh.ttf']),
                    ('SimSun', ['C:/Windows/Fonts/simsun.ttc', 'C:/Windows/Fonts/simsun.ttf']),
                    ('KaiTi', ['C:/Windows/Fonts/simkai.ttf']),
                    ('Microsoft JhengHei', ['C:/Windows/Fonts/msjh.ttc']),
                ]
            elif system == 'Darwin':  # macOS
                font_configs = [
                    ('PingFang SC', ['/System/Library/Fonts/PingFang.ttc']),
                    ('Heiti SC', ['/System/Library/Fonts/STHeiti Light.ttc']),
                    ('STHeiti', ['/System/Library/Fonts/STHeiti Medium.ttc']),
                    ('Arial Unicode MS', ['/Library/Fonts/Arial Unicode.ttf']),
                ]
            else:  # Linux
                font_configs = [
                    ('WenQuanYi Micro Hei', ['/usr/share/fonts/truetype/wqy/wqy-microhei.ttc']),
                    ('Droid Sans Fallback', ['/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf']),
                    ('Noto Sans CJK SC', ['/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc']),
                ]

            # 尝试设置字体
            font_set = False
            working_font = None

            for font_name, font_paths in font_configs:
                # 检查字体文件是否存在
                font_exists = any(os.path.exists(path) for path in font_paths)

                if font_exists:
                    try:
                        # 尝试设置字体
                        plt.rcParams['font.sans-serif'] = [font_name] + plt.rcParams['font.sans-serif']
                        plt.rcParams['axes.unicode_minus'] = False

                        # 测试字体是否能正确显示中文
                        test_fig, test_ax = plt.subplots(figsize=(1, 1))
                        test_ax.text(0.5, 0.5, '测试', fontsize=12)
                        plt.close(test_fig)

                        font_set = True
                        working_font = font_name
                        self.log_signal.emit(f"RectPack成功设置中文字体: {font_name}")
                        break

                    except Exception as e:
                        self.log_signal.emit(f"RectPack字体 {font_name} 设置失败: {str(e)}")
                        continue

            if not font_set:
                # 如果都失败了，尝试系统默认字体
                try:
                    # 获取系统中所有可用的中文字体
                    available_fonts = [f.name for f in fm.fontManager.ttflist]
                    chinese_fonts = [f for f in available_fonts if any(keyword in f for keyword in
                                   ['SimHei', 'SimSun', 'Microsoft', 'YaHei', 'PingFang', 'Heiti', 'WenQuanYi', 'Noto'])]

                    if chinese_fonts:
                        plt.rcParams['font.sans-serif'] = chinese_fonts[:3] + ['DejaVu Sans']
                        plt.rcParams['axes.unicode_minus'] = False
                        working_font = chinese_fonts[0]
                        font_set = True
                        self.log_signal.emit(f"RectPack使用系统检测到的中文字体: {working_font}")
                    else:
                        # 最后的备选方案
                        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
                        plt.rcParams['axes.unicode_minus'] = False
                        self.log_signal.emit("RectPack警告: 未找到中文字体，使用默认字体，中文可能显示为方块")

                except Exception as e:
                    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
                    plt.rcParams['axes.unicode_minus'] = False
                    self.log_signal.emit(f"RectPack字体检测失败: {str(e)}，使用默认字体")

            # 清除matplotlib字体缓存，确保新设置生效
            try:
                fm._rebuild()
            except:
                pass

        except Exception as e:
            self.log_signal.emit(f"RectPack设置中文字体失败: {str(e)}")
